import os
from app import create_app, db, mongo, socketio
from app.models import User, Tournament, Team, Player, Match, TournamentTeam
from flask_migrate import upgrade


app = create_app(os.getenv('FLASK_CONFIG') or 'default')


@app.shell_context_processor
def make_shell_context():
    """Make database models available in flask shell."""
    return {
        'db': db,
        'mongo': mongo,
        'mongo_models': app.mongo_models,
        'User': User,
        'Tournament': Tournament,
        'Team': Team,
        'Player': Player,
        'Match': Match,
        'TournamentTeam': TournamentTeam
    }


@app.cli.command()
def deploy():
    """Run deployment tasks."""
    # Create database tables (SQLAlchemy - for backward compatibility)
    upgrade()

    # Initialize MongoDB collections and indexes
    print("Initializing MongoDB...")
    for model_name, model in app.mongo_models.items():
        print(f"Creating indexes for {model_name}")
        model.create_indexes()
    print("MongoDB initialization completed!")


@app.cli.command()
def init_mongo():
    """Initialize MongoDB collections and indexes."""
    print("Initializing MongoDB...")
    for model_name, model in app.mongo_models.items():
        print(f"Creating indexes for {model_name}")
        model.create_indexes()
    print("MongoDB initialization completed!")


if __name__ == '__main__':
    socketio.run(app, debug=True, host='0.0.0.0', port=5002)
