from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_pymongo import PyMongo
from flask_login import <PERSON>ginManager
from flask_cors import CORS
from flask_socketio import <PERSON>cket<PERSON>
from config import config

# Initialize extensions
db = SQLAlchemy()  # Keep for backward compatibility during transition
migrate = Migrate()  # Keep for backward compatibility during transition
mongo = PyMongo()
login_manager = LoginManager()
socketio = SocketIO()


def create_app(config_name='default'):
    """Application factory pattern."""
    app = Flask(__name__)
    app.config.from_object(config[config_name])

    # Initialize extensions with app
    db.init_app(app)  # Keep for backward compatibility during transition
    migrate.init_app(app, db)  # Keep for backward compatibility during transition
    mongo.init_app(app)
    login_manager.init_app(app)
    CORS(app)
    socketio.init_app(app, cors_allowed_origins="*")

    # Initialize Discord service
    from app.services.discord_service import discord_service
    discord_service.init_app(app)

    # Initialize Stream service
    from app.services.stream_service import stream_service
    stream_service.init_app(app)
    
    # Configure login manager
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'Please log in to access this page.'
    login_manager.login_message_category = 'info'
    
    # Register blueprints
    from app.auth import bp as auth_bp
    app.register_blueprint(auth_bp, url_prefix='/auth')
    
    from app.main import bp as main_bp
    app.register_blueprint(main_bp)
    
    from app.tournaments import bp as tournaments_bp
    app.register_blueprint(tournaments_bp, url_prefix='/tournaments')
    
    from app.teams import bp as teams_bp
    app.register_blueprint(teams_bp, url_prefix='/teams')
    
    from app.matches import bp as matches_bp
    app.register_blueprint(matches_bp, url_prefix='/matches')

    from app.leaderboards import bp as leaderboards_bp
    app.register_blueprint(leaderboards_bp, url_prefix='/leaderboards')

    from app.admin import bp as admin_bp
    app.register_blueprint(admin_bp, url_prefix='/admin')

    from app.api import bp as api_bp
    app.register_blueprint(api_bp, url_prefix='/api')

    from app.streams import bp as streams_bp
    app.register_blueprint(streams_bp, url_prefix='/streams')

    # Import models to ensure they are registered with SQLAlchemy
    from app import models

    # Initialize MongoDB models and create indexes
    from app.mongo_models import User, Tournament, Team, Player, TournamentTeam, Match, Game, Stream

    # Store model instances in app context for easy access
    with app.app_context():
        app.mongo_models = {
            'User': User(mongo.db),
            'Tournament': Tournament(mongo.db),
            'Team': Team(mongo.db),
            'Player': Player(mongo.db),
            'TournamentTeam': TournamentTeam(mongo.db),
            'Match': Match(mongo.db),
            'Game': Game(mongo.db),
            'Stream': Stream(mongo.db)
        }

        # Create indexes for all collections
        for model in app.mongo_models.values():
            model.create_indexes()

    # Import WebSocket events
    from app import websocket_events

    return app


@login_manager.user_loader
def load_user(user_id):
    """Load user for Flask-Login."""
    from flask import current_app
    from bson import ObjectId

    try:
        # Handle both ObjectId strings and legacy integer IDs
        if isinstance(user_id, str):
            if len(user_id) == 24:
                # This looks like a MongoDB ObjectId
                try:
                    user_id = ObjectId(user_id)
                except:
                    return None
            else:
                # This might be a legacy integer ID, skip for now
                return None

        user_model = current_app.mongo_models['User']
        user_doc = user_model.find_by_id(user_id)
        if user_doc:
            # Create a user object that works with Flask-Login
            class MongoUser:
                def __init__(self, user_doc):
                    self._id = user_doc['_id']
                    self.username = user_doc['username']
                    self.email = user_doc['email']
                    self.password_hash = user_doc['password_hash']
                    self.first_name = user_doc['first_name']
                    self.last_name = user_doc['last_name']
                    self.role = user_doc['role']
                    self.is_active = user_doc['is_active']
                    self.created_at = user_doc['created_at']
                    self.updated_at = user_doc.get('updated_at')
                    self.avatar_url = user_doc.get('avatar_url')
                    self.bio = user_doc.get('bio')
                    self.discord_username = user_doc.get('discord_username')
                    self.twitch_username = user_doc.get('twitch_username')

                def get_id(self):
                    return str(self._id)

                @property
                def full_name(self):
                    return f"{self.first_name} {self.last_name}"

                def check_password(self, password):
                    from app.mongo_models import User
                    return User.check_password(self.password_hash, password)

                def is_authenticated(self):
                    return True

                def is_anonymous(self):
                    return False

            return MongoUser(user_doc)
    except Exception as e:
        # Silently fail for invalid user IDs (common during transition)
        pass

    return None
