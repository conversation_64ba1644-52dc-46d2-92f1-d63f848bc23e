# Team management routes - to be implemented
from flask import render_template, redirect, url_for, flash, request, current_app
from flask_login import login_required, current_user
from bson import ObjectId
from app.teams import bp
from app.models import Team, Player, UserRole
from app.mongo_models import UserRole as MongoUserRole
from app.services.team_service import TeamService
from app import db


@bp.route('/')
def index():
    """List all teams with search and filtering."""
    # Get search parameters
    search = request.args.get('search', '').strip()
    sort_by = request.args.get('sort', 'name')  # name, created_at, players_count
    order = request.args.get('order', 'asc')  # asc, desc
    min_players = request.args.get('min_players', type=int)
    max_players = request.args.get('max_players', type=int)
    has_captain = request.args.get('has_captain')  # 'yes', 'no', or None
    page = request.args.get('page', 1, type=int)
    per_page = 12  # Teams per page

    # Get MongoDB models
    team_model = current_app.mongo_models['Team']
    player_model = current_app.mongo_models['Player']

    # Build filter for active teams
    filter_dict = {'is_active': True}

    # Apply search filter
    if search:
        filter_dict['$or'] = [
            {'name': {'$regex': search, '$options': 'i'}},
            {'tag': {'$regex': search, '$options': 'i'}},
            {'description': {'$regex': search, '$options': 'i'}}
        ]

    # Apply captain filter
    if has_captain == 'yes':
        filter_dict['captain_id'] = {'$ne': None}
    elif has_captain == 'no':
        filter_dict['captain_id'] = None

    # Get total count for pagination
    total = team_model.count_documents(filter_dict)

    # Calculate pagination
    skip = (page - 1) * per_page
    has_prev = page > 1
    has_next = skip + per_page < total

    # Determine sort order
    sort_direction = 1 if order == 'asc' else -1
    sort_field = 'name' if sort_by == 'name' else 'created_at'

    # Get teams with pagination (simplified for now)
    teams_cursor = team_model.find(filter_dict).sort(sort_field, sort_direction).skip(skip).limit(per_page)
    teams_list = list(teams_cursor)

    # For each team, get player count
    for team in teams_list:
        team['player_count'] = player_model.count_documents({
            'team_id': team['_id'],
            'is_active': True
        })

    # Apply player count filters if specified
    if min_players is not None:
        teams_list = [team for team in teams_list if team.get('player_count', 0) >= min_players]
    if max_players is not None:
        teams_list = [team for team in teams_list if team.get('player_count', 0) <= max_players]

    # Create pagination object-like structure
    class Pagination:
        def __init__(self, items, page, per_page, total, has_prev, has_next):
            self.items = items
            self.page = page
            self.per_page = per_page
            self.total = total
            self.has_prev = has_prev
            self.has_next = has_next
            self.prev_num = page - 1 if has_prev else None
            self.next_num = page + 1 if has_next else None
            self.pages = (total + per_page - 1) // per_page

    teams = Pagination(teams_list, page, per_page, total, has_prev, has_next)

    return render_template('teams/index.html',
                         title='Teams',
                         teams=teams,
                         search=search,
                         sort_by=sort_by,
                         order=order,
                         min_players=min_players,
                         max_players=max_players,
                         has_captain=has_captain)


@bp.route('/create')
def create():
    """Create new team."""
    return "Create team - to be implemented"


@bp.route('/<team_id>')
def view(team_id):
    """View team details."""
    team_model = current_app.mongo_models['Team']
    player_model = current_app.mongo_models['Player']
    tournament_team_model = current_app.mongo_models['TournamentTeam']

    # Convert string ID to ObjectId if needed
    if isinstance(team_id, str) and len(team_id) == 24:
        try:
            team_id = ObjectId(team_id)
        except:
            abort(404)
    else:
        # Handle integer IDs for backward compatibility
        team = team_model.find_one({'legacy_id': int(team_id)})
        if not team:
            abort(404)
        team_id = team['_id']

    team = team_model.find_one({'_id': team_id})
    if not team or not team.get('is_active', True):
        flash('This team is no longer active.', 'error')
        return redirect(url_for('teams.index'))

    # Get team members
    members = list(player_model.find({'team_id': team_id, 'is_active': True}))

    # Get tournament registrations
    tournament_registrations = list(tournament_team_model.find({
        'team_id': team_id,
        'is_active': True
    }))

    # Basic team stats (simplified)
    team_stats = {
        'total_members': len(members),
        'tournaments_participated': len(tournament_registrations)
    }

    return render_template('teams/view.html',
                         title=f'{team["name"]} - Team Details',
                         team=team,
                         members=members,
                         tournament_registrations=tournament_registrations,
                         team_stats=team_stats)


@bp.route('/my-teams')
@login_required
def my_teams():
    """View teams where current user is captain or member."""
    if current_user.role != MongoUserRole.PLAYER.value:
        flash('Only players can view team memberships.', 'error')
        return redirect(url_for('teams.index'))

    # Get MongoDB models
    team_model = current_app.mongo_models['Team']
    player_model = current_app.mongo_models['Player']

    # Find player profile for current user
    player = player_model.find_by_user_id(current_user._id)
    if not player:
        flash('You need to complete your player profile first.', 'info')
        return redirect(url_for('auth.profile'))

    # Get teams where user is captain
    captain_teams = list(team_model.find({
        'captain_id': player['_id'],
        'is_active': True
    }).sort('created_at', -1))

    # Get team where user is a member (if any)
    member_team = None
    if player.get('team_id'):
        member_team = team_model.find_one({
            '_id': player['team_id'],
            'is_active': True
        })

    return render_template('teams/my_teams.html',
                         title='My Teams',
                         captain_teams=captain_teams,
                         member_team=member_team)
