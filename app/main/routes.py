from flask import render_template, request, current_app
from app.main import bp
from app.models import Tournament, Match, Team, Player, TournamentStatus
from app.mongo_models import TournamentStatus as MongoTournamentStatus
from app import db


@bp.route('/')
@bp.route('/index')
def index():
    """Home page with tournament listings and statistics."""
    # Get MongoDB models
    tournament_model = current_app.mongo_models['Tournament']
    team_model = current_app.mongo_models['Team']
    player_model = current_app.mongo_models['Player']
    match_model = current_app.mongo_models['Match']

    # Get featured tournaments (open tournaments)
    featured_tournaments = list(tournament_model.find({
        'status': MongoTournamentStatus.REGISTRATION_OPEN.value,
        'is_active': True
    }).sort('created_at', -1).limit(4))

    # Get statistics
    tournament_count = tournament_model.count_documents({'is_active': True})
    team_count = team_model.count_documents({'is_active': True})
    player_count = player_model.count_documents({'is_active': True})
    match_count = match_model.count_documents({})

    return render_template('main/index.html',
                         title='Home',
                         featured_tournaments=featured_tournaments,
                         tournament_count=tournament_count,
                         team_count=team_count,
                         player_count=player_count,
                         match_count=match_count)


@bp.route('/about')
def about():
    """About page."""
    return render_template('main/about.html')


@bp.route('/contact')
def contact():
    """Contact page."""
    return render_template('main/contact.html')
